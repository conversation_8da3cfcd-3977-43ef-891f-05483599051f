package com.kuaishou.serveree.themis.api.ww;

import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.io.CharSource;
import com.google.common.io.Resources;
import com.kuaishou.kconf.common.json.JsonMapperUtils;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-01-02
 */
@Data
@Slf4j
public class LocationEntity {

    private String city;
    private String province;

    public LocationEntity defaultLocation() {
        this.city = "";
        this.province = "";
        return this;
    }

    private static String regex =
            "(?<province>[^省]+省|.+自治区|[^澳门]+澳门|北京|重庆|上海|天津|台湾|[^香港]+香港|[^市]+市)?"
                    + "(?<city>[^自治州]+自治州|[^特别行政区]+特别行政区|[^市]+市|.*?地区|.*?行政单位|.+盟|市辖区|[^县]+县)"
                    + "(?<county>[^县]+县|[^市]+市|[^镇]+镇|[^区]+区|[^乡]+乡|.+场|.+旗|.+海域|.+岛)?(?<address>.*)";

    //
    private static String cityRegex =
            "(?<city>[^自治州]+自治州|[^特别行政区]+特别行政区|[^市]+市|.*?地区|.*?行政单位|.+盟|市辖区|[^县]+县)";
    private static String provinceRegex =
            "(?<province>[^省]+省|.+自治区|[^澳门]+澳门|台湾|[^香港]+香港)?";

    private static Pattern cityPattern = Pattern.compile(cityRegex);
    private static Pattern provincePattern = Pattern.compile(provinceRegex);
    private static Pattern pattern = Pattern.compile(regex);


    public static LocationEntity getLocation(String address) {
        LocationEntity locationEntity = new LocationEntity().defaultLocation();
        if (Strings.isNullOrEmpty(address)) {
            return locationEntity.defaultLocation();
        }
        Matcher matcher = pattern.matcher(address);

        if (matcher.find()) {
            String province = matcher.group("province");
            String city = matcher.group("city");

            locationEntity.setCity(ProvinceCityMapping.getCity(city));

            final String cityProvince = ProvinceCityMapping.getProvince(locationEntity.getCity());
            if (StringUtils.isNoneBlank(cityProvince)) {
                province = cityProvince;
            }
            locationEntity.setProvince(province);
        }

        if (!locationEntity.validProvinceCity()) {
            return locationEntity.defaultLocation();
        }
        return locationEntity;
    }

    public static String getProvince(String address) {
        Matcher matcher = provincePattern.matcher(address);
        if (matcher.find()) {
            return matcher.group("province");
        }

        return "";
    }

    public static String getCity(String address) {
        Matcher matcher = cityPattern.matcher(address);
        if (matcher.find()) {
            return matcher.group("city");
        }

        return "";
    }

    public static LocationEntity of(String address) {
        LocationEntity location = new LocationEntity();
        if (Strings.isNullOrEmpty(address)) {
            return location.defaultLocation();
        }
        location = getLocation(address);
        if (location.validProvinceCity()) {
            return location;
        }

        String city = getCity(address);
        String province = getProvince(address);

        if (LocationEntity.validProvince(province)) {
            location.setProvince(province);
        }
        if (LocationEntity.validCity(city)) {
            location.setCity(city);

            if (Strings.isNullOrEmpty(province)) {
                location.setProvince(ProvinceCityMapping.getProvince(city));
            }
        }

        if (!location.validProvinceCity()) {
            location = location.defaultLocation();
        }
        return location;

    }

    public static boolean validProvince(String province) {
        if (Strings.isNullOrEmpty(province)) {
            return false;
        }
        Set<String> provinceSet = Sets.newHashSet(ProvinceCityMapping.cityProvinceMap.values());
        if (provinceSet.contains(province)) {
            return true;
        }

        return false;
    }

    public static boolean validCity(String city) {
        if (Strings.isNullOrEmpty(city)) {
            return false;
        }
        Set<String> citySet = Sets.newHashSet(ProvinceCityMapping.cityProvinceMap.keySet());
        if (citySet.contains(city)) {
            return true;
        }

        return true;
    }

    public boolean validProvinceCity() {

        if (Strings.isNullOrEmpty(city) && Strings.isNullOrEmpty(province)) {
            return false;
        }

        if (validProvince(province) && validCity(city)) {
            return true;
        }

        return false;
    }


    public static class ProvinceCityMapping {

        private static HashMap<String, String> cityProvinceMap = Maps.newHashMap();
        private static HashMap<String, String> countyCityMap = Maps.newHashMap();


        static {
            try {
                URL resource = ProvinceCityMapping.class.getResource("/province_city.json");
                log.info("resource={}", resource);
                final CharSource charSource = Resources.asCharSource(resource, StandardCharsets.UTF_8);
                final HashMap provinceMap = JsonMapperUtils.fromJson(charSource.read(), HashMap.class);

                for (Object province : provinceMap.keySet()) {
                    countyCityMap.put(province.toString(), province.toString());


                    HashMap cityMap = (HashMap) (provinceMap.get(province));
                    for (Object city : cityMap.keySet()) {
                        countyCityMap.put(city.toString(), city.toString());
                        cityProvinceMap.put(city.toString(), province.toString());
                        ArrayList countyList = (ArrayList) cityMap.get(city);
                        for (Object county : countyList) {
                            cityProvinceMap.put(county.toString(), province.toString());
                            countyCityMap.put(county.toString(), city.toString());
                        }
                    }
                }
            } catch (Exception e) {
                log.error("ProvinceCityUtils.init", e);
            }
        }

        public static String getProvince(String province) {
            return cityProvinceMap.getOrDefault(province, "");
        }

        public static String getCity(String city) {
            return countyCityMap.getOrDefault(city, "");
        }
    }

}

class Strings {
    public static boolean isNullOrEmpty(String str) {
        return str == null || str.isEmpty();
    }
}