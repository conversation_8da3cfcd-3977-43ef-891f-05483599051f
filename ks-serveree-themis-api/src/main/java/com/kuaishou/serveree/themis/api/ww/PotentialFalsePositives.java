package com.kuaishou.serveree.themis.api.ww;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 这个类展示了原始detector不会误报但修改后detector可能会误报的各种情况
 */
public class PotentialFalsePositives {

    // 案例1: 变量可能为null但在特定分支中安全使用
    public void processSafely(String input) {
        if (input != null) {
            // 这个分支中input绝对不为null
            input.length();
        } else {
            // 另一个安全处理逻辑
            System.out.println("Input is null");
        }
    }

    // 案例2: 短路逻辑中的安全检查
    public void shortCircuitSafe(User user) {
        if (user != null && user.getName().startsWith("A")) {
            // 这里user一定不为null
            doSomethingWith(user);
        }
    }

    private void doSomethingWith(User user) {
        // 这里如果从shortCircuitSafe调用，user一定不为null
        user.getId(); // 使用user
    }

    // 案例3: 初始化后的字段使用
    private List<String> items;
    
    public PotentialFalsePositives() {
        this.items = new ArrayList<>();
    }
    
    public void addItem(String item) {
        // 构造函数确保items不为null
        items.add(item);
    }

    // 案例4: 集合类型的API保证
    public void processMap(Map<String, Object> map) {
        // map不为null，但map.get可能返回null
        Object value = map.get("key");
        
        if (map.containsKey("key")) {
            // 如果包含key，那么value不会为null
            value.toString();
        }
    }

    // 案例5: 懒加载初始化模式
    private Resource resource;
    
    public Resource getResource() {
        if (resource == null) {
            resource = new Resource();
        }
        return resource; // 返回时一定不为null
    }
    
    public void useResource() {
        getResource().process(); // 这里的调用是安全的
    }

    // 案例6: 不可达的空值分支
    public void unreachableNullBranch(String input) {
        // 假设这个方法的契约是input总是非null
        if (input == null) {
            throw new IllegalArgumentException("Input cannot be null");
            // 这之后的代码永远不会执行
        }
        
        if (someComplexCondition()) {
            input.length(); // 总是安全的，因为null检查
        } else {
            // 复杂的代码路径...
            input.trim(); // 同样总是安全的
        }
    }

    private boolean someComplexCondition() {
        return System.currentTimeMillis() % 2 == 0;
    }

    // 案例7: Optional的使用
    public void processOptionalValue(Optional<String> optional) {
        if (optional.isPresent()) {
            String value = optional.get(); // 总是安全的
            value.length();
        }
    }

    // 案例8: 断言或前置条件库
    public void processWithPrecondition(String input) {
        // 使用前置条件库
        checkNotNull(input, "Input must not be null");
        
        // 一些复杂逻辑...
        
        input.length(); // 安全的，因为有前置条件检查
    }
    
    // 简化版的前置条件检查方法
    private <T> T checkNotNull(T reference, String message) {
        if (reference == null) {
            throw new NullPointerException(message);
        }
        return reference;
    }

    // 案例9: 工厂方法保证
    public void useProduct() {
        Product p = Factory.createProduct();
        p.use(); // 安全的，因为工厂方法保证
    }

    // 案例10: 本地非null变量重用
    public void reuseLocalVariable(boolean condition) {
        String value = "default"; // 初始非null
        
        if (condition) {
            // 一些复杂逻辑...
            value = getSomeValue(); // 可能返回null
        }
        
        if (value != null) {
            value.length(); // 安全的，有null检查
        }
    }
    
    private String getSomeValue() {
        return Math.random() > 0.5 ? "value" : null;
    }
    
    // 案例11: 自定义null检查工具方法
    public void useCustomNullCheck(Data data) {
        if (isNotNull(data)) {
            data.process(); // 安全的，有自定义null检查
        }
    }
    
    private boolean isNotNull(Object obj) {
        return obj != null;
    }
    
    // 案例12: try-with-resources模式
    public void readFile(String path) {
        try (InputStream is = Files.newInputStream(Paths.get(path))) {
            // try-with-resources保证is非空
            is.read(); // 实际上是安全的
        } catch (IOException e) {
            // 处理异常
        }
    }
    
    // 内部类和接口定义，以保证代码完整性
    
    static class User {
        public String getName() {
            return "User";
        }
        
        public int getId() {
            return 1;
        }
    }
    
    static class Resource {
        public void process() {
            System.out.println("Processing resource");
        }
    }
    
    static class Product {
        public void use() {
            System.out.println("Using product");
        }
    }
    
    static class Factory {
        public static Product createProduct() {
            return new Product();
        }
    }
    
    static class Data {
        public void process() {
            System.out.println("Processing data");
        }
    }
}