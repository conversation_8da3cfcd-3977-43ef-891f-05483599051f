package com.kuaishou.serveree.themis.api.ww;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.locks.ReentrantLock;

import com.google.common.base.MoreObjects;
import com.kuaishou.serveree.themis.component.vo.response.RepoSearchResponse.RepoInfo.RepoLanguage;
import com.kuaishou.serveree.themis.component.vo.response.RepoSettingResponse;
import com.kuaishou.serveree.themis.component.vo.response.RepoSettingResponse.OfflineScanSetting;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-06-17
 */
@Slf4j
@Data
public class SubClassTest extends Department {

    private String sub;
}

class TryChecker {
    public static void main(String[] args) {
        ReentrantLock lock = new ReentrantLock();
        lock.lock(); // Noncompliant
        System.out.println("hello world");
        try { // Noncompliant
            lock.lock();
            try { // Noncompliant
                lock.lock();
            } finally {

            }
        } finally {

        }
        try { // Noncompliant
            try { // Noncompliant
                lock.lock();
            } finally {

            }
            lock.lock();
        } finally {

        }
        try { // Noncompliant
            try { // Noncompliant
                lock.lock();
            } finally {

            }
            lock.lock();
        } finally {

        }
        lock.lock(); // Noncompliant
        try {

        } catch (Exception ignore) {

        }
        try { // Compliant
            lock.tryLock(); // Compliant
        } catch (Exception ignore) {

        }
    }
}

@Slf4j
class ToArrayChec1111k<T> {
    <E extends T> Object[] foo(List<String> listOfString, List<String> listOfNumber, Set rawSet, Collection<E> col) {
        String[] a488 = listOfString.toArray(new String[0]); // Compliant
        String[] a48 = listOfString.toArray(String[]::new); // Compliant
        String[] a4888 = listOfString.toArray(new String[]{}); // Compliant
        String[] a48888 = listOfString.toArray(new String[]{     }); // Compliant
        String[] a488888 = listOfString.toArray(new String[]{"a"}); // Noncompliant
        String[] a47 = listOfString.toArray(new String[8]); // Noncompliant
        String[] a49 = listOfString.toArray(new String[listOfString.size()]); // Noncompliant
        String[] a1 = (String[]) listOfString.toArray(); // Noncompliant
        Object[] a3 = listOfString.toArray(); // Compliant
        Object[] a5 = (Object[]) listOfString.toArray(); // Compliant
        E[] a6 = (E[]) col.toArray(); // Compliant
        Object o = (Object) listOfString.toArray(); // Compliant

        Number[] a7 = (Number[]) rawSet.toArray(new Number[0]);
        Number[] a8 = col.toArray(new Number[0]);

        return listOfString.toArray(); // Compliant
    }

    abstract class MySet<P> implements Set<P> {
        void callToArray() {
            String[] a1 = (String[]) toArray(); // Compliant
            String[] a2 = toArray(new String[0]); // Compliant
        }
    }

    private long f1;
    private long f2, f3 = 3l; // Noncompliant

    public void main(String[] args) {
        f1 = 123l; // Noncompliant
        String hash = "123";
        String aaa = "123l";
        long a = 123;
        long b = 456l; // Noncompliant
        long c = 789L;
        Long e = 456l; // Noncompliant
        Long f = 789L;
        long g;
        Long H;
        g = 111l; // Noncompliant
        H = 222l; // Noncompliant
        log.info("ccccc"
                + "ddddd"
                + "eeeee{}", hash);
        log.info("aaaaa"
                + "bbbbbb{}", hash);
        log.info("sss:", "sss" + "");
        log.info("are you kidding ? " + " yes !");
    }
}

class tryNoLogClass {
    public static void main(String[] args) {
        try {

        } catch (Exception e) {

        }

        try {

        } catch (Exception e) {
            // ...
        }
    }
}

@Slf4j
class NpeTest {
    // m + k + n + k = 2 (m + k)
    // m + k + n + k = m + m + k + k
    // n = m
    public void test1(RepoSettingResponse response) {
        long triggerTime = System.currentTimeMillis();
        List<RepoLanguage> evalResult = response.getLanguageSetting();
        OfflineScanSetting oldRights = response.getOfflineScanSetting();
        if (oldRights == null && (!Objects.equals(evalResult.get(0).getLanguage(), UUID.randomUUID().toString()))) {
        // if (oldRights == null) {
            // 开关关闭，不进行triggerTime离线数据的区分
            if (new Random().nextBoolean()) {
                if (triggerTime >= MoreObjects.firstNonNull(Long.valueOf(oldRights.getTriggerCron()), 0L)) { // Noncompliant
                    log.info("grant new right, merchant id: {}, new rights: {}", "aa", "bb");
                    return;
                }
            }
        }
    }

    public void test2(RepoSettingResponse response) {
        long triggerTime = System.currentTimeMillis();
        List<RepoLanguage> evalResult = response.getLanguageSetting();
        OfflineScanSetting oldRights = response.getOfflineScanSetting();
        if (oldRights == null || (!Objects.equals(evalResult.get(0).getLanguage(), oldRights.getBranch()))) {
            // if (oldRights == null) {
            // 开关关闭，不进行triggerTime离线数据的区分
            if (new Random().nextBoolean()) {
                if (triggerTime >= MoreObjects.firstNonNull(Long.valueOf(oldRights.getTriggerCron()), 0L)) { // Noncompliant
                    log.info("grant new right, merchant id: {}, new rights: {}", "aa", "bb");
                    return;
                }
            }
        }
    }
}

class Test {

    private Object abc() {
        return null;
    }

    private void def() {
        Test test = new Test();
        Object obj = test.abc();
        System.out.println(obj.toString()); // Noncompliant
    }
}

class FloatDemo {
    public static void main(String[] args) {
        float f = 1.2f;
        Float ff = 1.2f, fff = 1.2f;
        double d = 1.2d;
        Double dd = 1.2d, ddd = 1.3d;
        if (d == (1.0 + 2.5)) { // Noncompliant

        }
        if (dd == 1.0 * 2.3) { // Noncompliant

        }
        if (d == dd.doubleValue()) { // Noncompliant

        }
        if (f == d) { // Noncompliant

        }
        if (ff == fff) { // Noncompliant

        }
        if (f == ff) { // Noncompliant

        }
        if (dd == ddd) { // Noncompliant

        }
        if (dd.equals(ddd)) { // Noncompliant

        }
        if (dd.equals(d)) { // Noncompliant

        }
        if (dd != null) {

        }
    }
}

class TestBigDecimal {

    public void test(float a, double b) {
        BigDecimal bigDecimal = new BigDecimal(a);
        BigDecimal bigDecimal2 = new BigDecimal(2.4f);
        BigDecimal bigDecimal3 = new BigDecimal(3.8);
        BigDecimal bigDecimal34 = new BigDecimal("3.8");
        BigDecimal bigDecimal5 = new BigDecimal(a + b);
    }

    public boolean compare(BigDecimal a, BigDecimal b) {
        return a.equals(b);
    }

    public void test(BigDecimal a, BigDecimal b) {
        System.out.println(a.equals(b));
        if (a.equals(b)) {
            System.out.println("a equals b");
        }
        if (a.equals(BigDecimal.ZERO)) {
            System.out.println("a equals zero");
        }
        if (!BigDecimal.ZERO.equals(b)) {
            System.out.println("b not equals zero");
        }
        boolean c = a.equals(BigDecimal.ZERO);
        boolean d = BigDecimal.ZERO.equals(b);
    }
}

class ThreadLocalTest {
    private final ThreadLocal<Object> kconfParameter = ThreadLocal.withInitial(Person::new);
    private final ThreadLocal<Object> redisData = ThreadLocal.withInitial(Person::new);
    private final ThreadLocal<Map<String, Object>> itemLabelScoreMap = ThreadLocal.withInitial(() ->
            new HashMap<String, Object>() {{
                put("adjustWatchtimeScore", new Object());
                put("formulaWatchtimeScore", new Object());
                put("clickScore", new Object());
                put("clickWatchtimeScore", new Object());
                put("pwatchtimeRankScore", new Object());
            }});
}

class A {
    private int age;
}

@Data
class B extends A {
    private String name;
}

@EqualsAndHashCode(callSuper = false)
@Data
class D extends A {
    private String name;
}

@EqualsAndHashCode(callSuper = true)
@Data
class C extends A {
    private String name;
}