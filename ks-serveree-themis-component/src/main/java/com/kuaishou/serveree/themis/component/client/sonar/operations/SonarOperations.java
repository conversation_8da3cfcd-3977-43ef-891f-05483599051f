package com.kuaishou.serveree.themis.component.client.sonar.operations;


import static com.google.common.base.CaseFormat.LOWER_CAMEL;
import static com.google.common.base.CaseFormat.LOWER_UNDERSCORE;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.kuaishou.serveree.themis.component.client.sonar.api.SonarCommonApi;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueStatus;
import com.kuaishou.serveree.themis.component.constant.sonar.Rating;
import com.kuaishou.serveree.themis.component.entity.sonar.Analyse;
import com.kuaishou.serveree.themis.component.entity.sonar.History;
import com.kuaishou.serveree.themis.component.entity.sonar.Issue;
import com.kuaishou.serveree.themis.component.entity.sonar.Level;
import com.kuaishou.serveree.themis.component.entity.sonar.Measure;
import com.kuaishou.serveree.themis.component.entity.sonar.Profiles;
import com.kuaishou.serveree.themis.component.entity.sonar.Project;
import com.kuaishou.serveree.themis.component.entity.sonar.SonarComponent;
import com.kuaishou.serveree.themis.component.entity.sonar.SonarMeasures;
import com.kuaishou.serveree.themis.component.entity.sonar.User;
import com.kuaishou.serveree.themis.component.entity.sonar.req.MeasureComponentTreeRequest;
import com.kuaishou.serveree.themis.component.entity.sonar.req.MeasuresComponentTreeRequest;
import com.kuaishou.serveree.themis.component.entity.sonar.req.QualityProfileReq;
import com.kuaishou.serveree.themis.component.entity.sonar.req.SonarSearchRequest;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.GetWebHooksResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.GetWebHooksResp.SonarWebHook;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.HistoryMeasureResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.HistoryMeasureResp.HistoryMeasures;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.IssuesResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.MeasureComponentResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.MeasureComponentTreeResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.MeasuresComponentTreeResponse;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.ProjectAnalyseResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.ProjectsResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.QualityProfileResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.UsersResp;

/**
 * <AUTHOR>
 * @since 2021/7/14 3:25 下午
 */
public interface SonarOperations {

    Logger logger = LoggerFactory.getLogger(SonarOperations.class);

    SonarCommonApi sonarApi();

    List<String> TYPES = Lists.newArrayList("BUG", "CODE_SMELL", "VULNERABILITY");

    List<String> SEVERITIES = Lists.newArrayList("BLOCKER", "CRITICAL", "MAJOR", "MINOR", "INFO");

    String TYPES_STRING = String.join(",", TYPES);
    String SEVERITIES_STRING = String.join(",", SEVERITIES);

    String SONAR_STATUS_RESOLVED = "RESOLVED";
    String SONAR_RESOLUTION_CLOSED = "FALSE-POSITIVE";
    String SONAR_RESOLUTION_WONTFIX = "WONTFIX";
    String SONAR_RESOLUTION_CLOSED_OR_WONTFIX = SONAR_RESOLUTION_CLOSED + "," + SONAR_RESOLUTION_WONTFIX;

    default List<Project> getAllProjects() {
        List<Project> all = new ArrayList<>();
        int pageNum = 0;
        final int pageSize = 500;
        ProjectsResp resp;
        do {
            resp = sonarApi().getProjects(++pageNum, pageSize);
            all.addAll(resp.getComponents());
        } while (pageNum * pageSize < resp.getPaging().getTotal());
        return all;
    }

    default String getAnalysesDate(String component, String from, String to) {
        ProjectAnalyseResp projectAnalyseResp = sonarApi().searchAnalyses(component, from, to);
        List<Analyse> analyses = projectAnalyseResp.getAnalyses();
        if (CollectionUtils.isEmpty(analyses)) {
            return StringUtils.EMPTY;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssZ");
        return simpleDateFormat.format(analyses.get(0).getDate());
    }

    default Measure getHistoryMeasure(String component, String metricKeys, String from, String to) {
        HistoryMeasureResp historyMeasureResp = sonarApi().getHistoryMeasure(component, metricKeys, from, to);
        List<Measure> measures = new ArrayList<>();

        List<HistoryMeasures> measuresList = historyMeasureResp.getMeasures();
        for (HistoryMeasures historyMeasures : measuresList) {
            String metric = historyMeasures.getMetric();
            List<History> history = historyMeasures.getHistory();
            try {
                Field field = Measure.class.getDeclaredField(
                        Objects.requireNonNull(LOWER_UNDERSCORE.converterTo(LOWER_CAMEL).convert(metric))
                );
                int index = 0;
                for (History historyMeasure : history) {
                    if (measures.size() <= index) {
                        measures.add(new Measure());
                    }
                    fillMeasureField(field, measures.get(index++), historyMeasure.getValue());
                }
            } catch (NoSuchFieldException | IllegalAccessException e) {
                logger.error("sonar report reflection package args error!", e);
            }
        }
        return Iterables.getLast(measures, null);
    }

    default void fillMeasureField(Field field, Measure measure, String value)
            throws IllegalAccessException {
        field.setAccessible(true);
        if (field.getType() == Rating.class) {
            field.set(measure, Rating.values()[new BigDecimal(value).intValue() - 1]);
        } else if (field.getType() == Level.class) {
            field.set(measure, Level.valueOf(value.toUpperCase()));
        } else if (field.getType() == Integer.class) {
            field.set(measure, value == null ? 0 : Integer.parseInt(value));
        }
    }

    default int getBugsCount(String component, String from, String to, Boolean resolved) {
        SonarSearchRequest searchRequest = SonarSearchRequest.builder()
                .componentKeys(component)
                .from(from)
                .to(to)
                .resolved(resolved)
                .types("BUG")
                .build();
        IssuesResp issuesResp = sonarApi().searchIssues(searchRequest);
        return issuesResp.getPaging().getTotal();
    }

    default int getIssuesCount(String component, String from, String to, String type, Boolean resolved) {
        SonarSearchRequest searchRequest = SonarSearchRequest.builder()
                .componentKeys(component)
                .from(from)
                .to(to)
                .types(type)
                .resolved(resolved)
                .build();
        IssuesResp issuesResp = sonarApi().searchIssues(searchRequest);
        return issuesResp.getPaging().getTotal();
    }

    /**
     * 获取所有sonar用户
     *
     * @return List<User>
     */
    default List<User> getAllUsers() {
        List<User> all = new ArrayList<>();
        int pageNum = 0;
        final int pageSize = 500;
        UsersResp resp;
        do {
            resp = sonarApi().getUsers(++pageNum, pageSize);
            all.addAll(resp.getUsers());
        } while (pageNum * pageSize < resp.getPaging().getTotal());
        return all;
    }

    default List<String> getProjectHookUrls(String component) {
        GetWebHooksResp webHooks = sonarApi().getWebHooks(component);
        if (webHooks == null || CollectionUtils.isEmpty(webHooks.getWebhooks())) {
            return Lists.newArrayList();
        }
        return webHooks.getWebhooks().stream().map(SonarWebHook::getUrl).collect(Collectors.toList());
    }

    default Map<String, List<Issue>> getOpenIssuesWithoutCodeSmell(String projectKey, String branch,
            String analysedAt) {
        SonarSearchRequest searchRequest = SonarSearchRequest.builder()
                .componentKeys(projectKey)
                .statuses("OPEN,REOPENED")
                .types("BUG,VULNERABILITY,SECURITY_HOTSPOT")
                .severities("BLOCKER,CRITICAL,MAJOR")
                .resolved(false)
                .branch(branch)
                .build();
        IssuesResp issuesResp = sonarApi().searchIssues(searchRequest);
        return issuesResp.getIssues().stream()
                .filter(issue -> Objects.nonNull(issue.getAssignee()) && analysedAt.equals(issue.getUpdateDate()))//
                .collect(Collectors.groupingBy(Issue::getAssignee));
    }

    default Map<String, List<Issue>> getClosedIssuesWithoutCodeSmell(String projectKey, String branch,
            String closedAt) {
        IssuesResp resp = sonarApi().searchClosedIssues(projectKey, branch);
        return resp.getIssues().stream()
                .filter(issue -> Objects.nonNull(issue.getAssignee())
                        && Objects.equals(issue.getCloseDate(), closedAt)
                        && (!"CODE_SMELL".equalsIgnoreCase(issue.getType())))//
                .collect(Collectors.groupingBy(Issue::getAssignee));
    }

    default Map<String, List<Issue>> getOpenIssues(String projectKey, String branch, String analysedAt) {
        SonarSearchRequest searchRequest = SonarSearchRequest.builder()
                .componentKeys(projectKey)
                .statuses("OPEN,REOPENED")
                .severities("BLOCKER,CRITICAL,MAJOR")
                .resolved(false)
                .branch(branch)
                .build();
        return this.baseIssueList(searchRequest).stream() //
                .filter(issue -> Objects.nonNull(issue.getAssignee()) && analysedAt.equals(issue.getUpdateDate()))//
                .collect(Collectors.groupingBy(Issue::getAssignee));
    }

    default Map<String, List<Issue>> getClosedIssues(String projectKey, String branch, String closedAt) {
        IssuesResp resp = sonarApi().searchClosedIssues(projectKey, branch);
        return resp.getIssues().stream() //
                .filter(issue -> Objects.nonNull(issue.getAssignee()) && Objects.equals(issue.getCloseDate(), closedAt))
                .collect(Collectors.groupingBy(Issue::getAssignee));
    }

    default List<Issue> getAllIssues(String projectKey, String branchName) {
        SonarSearchRequest searchRequest = SonarSearchRequest.builder()
                .componentKeys(projectKey)
                .branch(branchName)
                .build();
        return this.baseIssueList(searchRequest);
    }

    default List<Issue> getAllIssuesDiffType(String projectKey, String branchName) {
        return getAllIssuesDiffType(projectKey, branchName, "OPEN,REOPENED");
    }

    default List<Issue> getAllIssuesDiffType(String projectKey, String branchName, String statuses) {
        List<Issue> issues = Lists.newArrayList();
        // 分类型
        for (String type : TYPES) {
            // 分级别
            for (String severity : SEVERITIES) {
                SonarSearchRequest searchRequest = SonarSearchRequest.builder()
                        .componentKeys(projectKey)
                        .branch(branchName)
                        .statuses(statuses)
                        .severities(severity)
                        .types(type)
                        .build();
                issues.addAll(this.baseIssueList(searchRequest));
            }
        }
        return issues;
    }

    default List<Issue> getAllIssuesMarkedAsClosedOrToReview(String projectKey, String branchName) {
        // sonarqube中误判、不修复后状态都是resoled，通过resolution区分
        SonarSearchRequest searchRequest = SonarSearchRequest.builder()
                .componentKeys(projectKey)
                .branch(branchName)
                .statuses(SONAR_STATUS_RESOLVED)
                .resolutions(SONAR_RESOLUTION_CLOSED_OR_WONTFIX)
                .severities(SEVERITIES_STRING)
                .types(TYPES_STRING)
                .build();
        // 把状态映射回平台上的状态
        return baseIssueList(searchRequest)
                .stream()
                .peek(i -> i.setStatus(SONAR_RESOLUTION_CLOSED.equals(i.getResolution())
                                       ? CheckIssueStatus.CLOSED.getStatus() : CheckIssueStatus.TO_REVIEW.getStatus()))
                .collect(Collectors.toList());
    }

    default List<Issue> baseIssueList(SonarSearchRequest sonarSearchRequest) {
        int page = 1;
        final List<Issue> allIssues = Lists.newArrayList();
        while (true) {
            sonarSearchRequest.setPage(page++);
            IssuesResp issuesResp = sonarApi().searchIssues(sonarSearchRequest);
            List<Issue> issues = issuesResp.getIssues();
            if (CollectionUtils.isEmpty(issues)) {
                break;
            }
            allIssues.addAll(issues);
            // 没有更多数据了
            if (allIssues.size() >= issuesResp.getTotal()) {
                break;
            }
        }
        return allIssues;
    }

    default MeasureComponentResp getMeasureData(String projectKey, String branch, String metricKeys) {
        return sonarApi().measuresComponent(projectKey, branch, null, metricKeys);
    }

    default List<Issue> getAllIncrementIssueList(String projectKey, String branchName) {
        SonarSearchRequest searchRequest = SonarSearchRequest.builder()
                .componentKeys(projectKey)
                .branch(branchName)
                .sinceLeakPeriod(true)
                .build();
        return this.baseIssueList(searchRequest);
    }

    default List<Issue> getAllIncrementIssueListDiffType(String projectKey, String branchName) {
        List<Issue> issues = Lists.newArrayList();
        for (String type : TYPES) {
            for (String severity : SEVERITIES) {
                SonarSearchRequest searchRequest = SonarSearchRequest.builder()
                        .componentKeys(projectKey)
                        .branch(branchName)
                        .types(type)
                        .severities(severity)
                        .sinceLeakPeriod(true)
                        .statuses("OPEN,REOPENED")
                        .build();
                issues.addAll(this.baseIssueList(searchRequest));
            }
        }
        return issues;
    }

    default List<Issue> getAllIncrementClosedIssues(String projectKey, String branchName) {
        SonarSearchRequest searchRequest = SonarSearchRequest.builder()
                .componentKeys(projectKey)
                .branch(branchName)
                .sinceLeakPeriod(true)
                .statuses("CLOSED")
                .build();
        return this.baseIssueList(searchRequest);
    }

    default String getProfileKeyByName(String profileName) {
        QualityProfileReq profileReq = QualityProfileReq.builder()
                .qualityProfile(profileName)
                .build();
        QualityProfileResp qualityProfile = sonarApi().getQualityProfile(profileReq);
        if (qualityProfile == null) {
            throw new ThemisException(-1, "qube端配置文件不存在");
        }
        List<Profiles> profiles = qualityProfile.getProfiles();
        if (CollectionUtils.isEmpty(profiles)) {
            throw new ThemisException(-1, "qube端配置文件为空");
        }
        return profiles.get(0).getKey();
    }

    default String getProfileKeyByName(String profileName, String language) {
        QualityProfileReq profileReq = QualityProfileReq.builder()
                .qualityProfile(profileName)
                .language(language)
                .build();
        QualityProfileResp qualityProfile = sonarApi().getQualityProfile(profileReq);
        if (qualityProfile == null) {
            throw new ThemisException(-1, "qube端配置文件不存在");
        }
        List<Profiles> profiles = qualityProfile.getProfiles();
        if (CollectionUtils.isEmpty(profiles)) {
            throw new ThemisException(-1, "qube端配置文件为空");
        }
        return profiles.get(0).getKey();
    }


    /**
     * 获取项目分支所有组件（文件）某一个度量指标
     */
    default List<SonarComponent> getAllMeasuresTree(String projectKey, String branchName, String metricKey) {
        List<SonarComponent> returnList = Lists.newArrayList();
        int page = 1;
        final int pageSize = 500;
        while (true) {
            MeasuresComponentTreeRequest treeRequest = MeasuresComponentTreeRequest.builder()
                    .metricKeys(metricKey)
                    .component(projectKey)
                    .branch(branchName)
                    .page(page)
                    .pageSize(pageSize)
                    .build();
            MeasuresComponentTreeResponse treeResponse = sonarApi().measuresComponentTree(treeRequest);
            List<SonarComponent> components = treeResponse.getComponents();
            if (CollectionUtils.isEmpty(components)) {
                break;
            }
            boolean attacheZero = false;
            for (SonarComponent component : components) {
                List<SonarMeasures> measures = component.getMeasures();
                SonarMeasures sonarMeasures = measures.get(0);
                if ("0".equals(sonarMeasures.getValue()) || "0.0".equals(sonarMeasures.getValue())) {
                    attacheZero = true;
                    break;
                } else {
                    returnList.add(component);
                }
            }
            if (attacheZero) {
                break;
            }
            page++;
        }
        return returnList;
    }

    /**
     * 获取项目分支所有组件（文件）某一些（逗号分隔）度量指标
     */
    default Map<String, SonarComponent> getAllComponentsMeasures(String projectKey, String branch, List<String> metricKeys) {
        // sonar一次只支持最多15个measureKey查询
        final int maxMetricKeys = 15;
        String metrics = metricKeys.size() > maxMetricKeys
                         ? StringUtils.join(metricKeys.subList(0, maxMetricKeys), ",")
                         : StringUtils.join(metricKeys, ",");
        List<SonarComponent> sonarComponentList = Lists.newArrayList();
        // 查所有文件的前15个指标
        final int pageSize = 500;
        MeasureComponentTreeRequest treeRequest = MeasureComponentTreeRequest.builder()
                .component(projectKey)
                .branch(branch)
                .pageSize(pageSize)
                .metricKeys(metrics)
                .metricSortFilter("all")
                .build();
        int page = 0;
        MeasureComponentTreeResp treeResponse;
        do {
            treeRequest.setPage(++page);
            treeResponse = sonarApi().measuresComponentTree(treeRequest);
            sonarComponentList.addAll(treeResponse.getComponents());
        } while (page * pageSize < treeResponse.getPaging().getTotal());
        Map<String, SonarComponent> path2ComponentMap =
                sonarComponentList.stream().collect(Collectors.toMap(SonarComponent::getPath, Function.identity()));
        if (metricKeys.size() <= maxMetricKeys) {
            return path2ComponentMap;
        }
        // 查询剩余指标
        metricKeys = metricKeys.subList(maxMetricKeys, metricKeys.size());
        Map<String, SonarComponent> restPath2ComponentMap = getAllComponentsMeasures(projectKey, branch, metricKeys);
        for (Entry<String, SonarComponent> entry : restPath2ComponentMap.entrySet()) {
            String path = entry.getKey();
            SonarComponent component = entry.getValue();
            // 合并到第一部分的结果中
            path2ComponentMap.get(path).getMeasures().addAll(component.getMeasures());
        }
        return path2ComponentMap;
    }
}
