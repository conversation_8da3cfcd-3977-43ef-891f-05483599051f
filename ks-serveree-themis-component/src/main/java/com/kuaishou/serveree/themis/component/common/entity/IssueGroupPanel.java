package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 最新生效的issue表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "IssueGroupPanel对象", description = "最新生效的issue表")
public class IssueGroupPanel extends IssueSummary implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer groupTime;

    private Integer groupType;

}
