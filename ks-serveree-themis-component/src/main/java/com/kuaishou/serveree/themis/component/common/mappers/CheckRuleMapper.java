package com.kuaishou.serveree.themis.component.common.mappers;

import java.util.Set;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.serveree.themis.component.common.entity.CheckRule;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-06
 */
@Mapper
@Repository
public interface CheckRuleMapper extends BaseMapper<CheckRule> {

    @Select("select rule_key from check_rule where deleted = 0 and can_skip = 0")
    Set<String> listRuleKeysCanNotBeSkipped();
}
