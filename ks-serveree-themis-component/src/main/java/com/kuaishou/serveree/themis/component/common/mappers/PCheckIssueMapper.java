package com.kuaishou.serveree.themis.component.common.mappers;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import com.kuaishou.serveree.themis.component.common.entity.PCheckIssue;
import com.kuaishou.serveree.themis.component.config.mybatis.RootMapper;

/**
 * <p>
 * 最新生效的issue表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-20
 */
@Mapper
@Repository
public interface PCheckIssueMapper extends RootMapper<PCheckIssue> {

    Integer issueGroupByCount(@Param("pBaseId") Long pBaseId,
            @Param("executionReferTypes") List<Integer> executionReferTypes);

    List<String> issueGroupByLocationList(@Param("pBaseId") Long pBaseId,
            @Param("executionReferTypes") List<Integer> executionReferTypes,
            @Param("start") long start, @Param("pageSize") long pageSize);

    @Select("SELECT distinct(execution_refer_type) FROM p_check_issue where p_base_id = #{pBaseId} order by execution_refer_type")
    List<Integer> getExecutionReferTypeByPCheckBase(@Param("pBaseId") Long pBaseId);

    @Select({"<script>",
            "select count(case when type='BUG' then id end) as BUG,",
            "count(case when type='VULNERABILITY' then id end) as VULNERABILITY,",
            "count(case when severity='MAJOR' then id end) as MAJOR,",
            "count(case when severity='SERIOUS' then id end) as SERIOUS,",
            "count(case when severity='COMMON' then id end) as COMMON,",
            "count(case when severity='NOT_INDUCED' then id end) as NOT_INDUCED,",
            "count(case when status='OPEN' then id end) as OPEN,",
            "count(case when status='RE_OPEN' then id end) as RE_OPEN ",
            "from `p_check_issue` where `p_base_id` = #{pBaseId} ",
            "<if test='executionReferType!=null'> and `execution_refer_type` = #{executionReferType} </if> ",
            "<if test='type!=null'> and `type` = #{type} </if> ",
            "<if test='severity!=null'> and `severity` = #{severity} </if> ",
            "<if test='filePathList != null and filePathList.size() > 0'> ",
            "and `location` in <foreach collection='filePathList' item='filePath' open='(' separator=',' close=')'> #{filePath} </foreach> ",
            "</if> ",
            "order by `id` desc ",
            "</script>"})
    Map<String, Integer> countFacetNum(@Param("pBaseId") Long pBaseId, @Param("executionReferType") Integer executionReferType,
            @Param("severity") String severity, @Param("type") String type, @Param("filePathList") List<String> filePathList);

}