package com.kuaishou.serveree.themis.component.constant.platform;

import java.util.List;
import java.util.Map;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/4/18 7:47 下午
 */
@AllArgsConstructor
public enum PlatformLanguageEnum {

    JAVA("java",
            Lists.newArrayList(PlatformScannerEnum.MAVEN_SCANNER_NEW),
            Lists.newArrayList("8", "17", "11"),
            Lists.newArrayList("Sonar way [kuaishou]")),
    JAVA_SCRIPT("javascript",
            Lists.newArrayList(PlatformScannerEnum.SKY_EYE),
            Lists.newArrayList("3", "5", "6", "7", "8", "9", "10", "11", "12", "13", "2015", "2016", "2017",
                    "2018", "2019", "2020", "2021", "2022"),
            Lists.newArrayList("skyeye-default")),
    TYPE_SCRIPT("typescript",
            Lists.newArrayList(PlatformScannerEnum.SKY_EYE),
            Lists.newArrayList("3.8", "3.9", "4.0", "4.1", "4.2", "4.3", "4.4", "4.5", "4.6"),
            Lists.newArrayList("53d75457664d43c090a56bdbbe5536a6")), // Vue3+TS基础规则集

    GO("go",
            Lists.newArrayList(PlatformScannerEnum.SONAR_SCANNER_NEW, PlatformScannerEnum.COVERITY_SCANNER),
            Lists.newArrayList("1.13", "1.14", "1.15.4", "1.16.7", "1.17.5", "1.18.2", "1.19", "1.20", "1.22"),
            Lists.newArrayList("992de0e7c0dd46ffa98c171d8e7d3185")), // go默认规则集

    CPP("c/c++",
            Lists.newArrayList(PlatformScannerEnum.COVERITY_SCANNER),
            Lists.newArrayList("c++11", "c++17", "c++20"),
            Lists.newArrayList("b6ce3111491d42f08454a1f8822d4426")), // cpp默认规则集
    ;

    @Getter
    private final String name;

    @Getter
    private final List<PlatformScannerEnum> platformScannerEnumList;

    @Getter
    private final List<String> versions;

    @Getter
    private final List<String> profiles;

    private static final Map<String, PlatformLanguageEnum> NAME_MAP = Maps.newHashMap();

    static {
        for (PlatformLanguageEnum platformLanguageEnum : PlatformLanguageEnum.values()) {
            NAME_MAP.put(platformLanguageEnum.getName(), platformLanguageEnum);
        }
        NAME_MAP.put("c", CPP);
        NAME_MAP.put("c++", CPP);
    }

    public static PlatformLanguageEnum getEnumByName(String name) {
        return NAME_MAP.get(name);
    }

}
