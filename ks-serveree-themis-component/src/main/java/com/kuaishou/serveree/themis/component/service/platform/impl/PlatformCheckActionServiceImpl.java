package com.kuaishou.serveree.themis.component.service.platform.impl;

import static com.kuaishou.serveree.themis.component.constant.ResultCodeConstant.NOT_FOUND_BASE_DATA;
import static java.util.stream.Collectors.toList;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.phantomthief.util.MoreFunctions;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.infra.framework.kafka.KafkaProducers;
import com.kuaishou.serveree.themis.component.common.entity.CheckBase;
import com.kuaishou.serveree.themis.component.common.entity.CheckComplexity;
import com.kuaishou.serveree.themis.component.common.entity.CheckDuplication;
import com.kuaishou.serveree.themis.component.common.entity.CheckExecution;
import com.kuaishou.serveree.themis.component.common.entity.CheckIssue;
import com.kuaishou.serveree.themis.component.common.entity.CheckMeasures;
import com.kuaishou.serveree.themis.component.common.entity.CheckMeasuresSnapshot;
import com.kuaishou.serveree.themis.component.common.entity.CheckMetric;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepo;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepoBranch;
import com.kuaishou.serveree.themis.component.common.entity.CheckSnapshot;
import com.kuaishou.serveree.themis.component.common.entity.IssueSummary;
import com.kuaishou.serveree.themis.component.common.entity.IssueSummaryBase;
import com.kuaishou.serveree.themis.component.common.entity.PCheckBase;
import com.kuaishou.serveree.themis.component.common.entity.PCheckConfig;
import com.kuaishou.serveree.themis.component.common.entity.PCheckExecution;
import com.kuaishou.serveree.themis.component.common.entity.PCheckIssue;
import com.kuaishou.serveree.themis.component.common.entity.PCheckMeasures;
import com.kuaishou.serveree.themis.component.common.entity.SonarPipelineMeasure;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.config.redis.KsRedisClient;
import com.kuaishou.serveree.themis.component.constant.platform.PlatformScannerEnum;
import com.kuaishou.serveree.themis.component.constant.platform.ScanModeEnum;
import com.kuaishou.serveree.themis.component.constant.plugin.IncrementType;
import com.kuaishou.serveree.themis.component.constant.process.ProcessExecutionReferType;
import com.kuaishou.serveree.themis.component.constant.process.ProcessScannerType;
import com.kuaishou.serveree.themis.component.constant.process.ProcessSponsorType;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueSeverity;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueStatus;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueType;
import com.kuaishou.serveree.themis.component.constant.redis.KsRedisPrefixConstant;
import com.kuaishou.serveree.themis.component.entity.kafka.CodeScanResultNotify;
import com.kuaishou.serveree.themis.component.entity.process.ScanModeContext;
import com.kuaishou.serveree.themis.component.entity.scanner.ScannerSendKimContext;
import com.kuaishou.serveree.themis.component.service.CheckBaseService;
import com.kuaishou.serveree.themis.component.service.CheckComplexityService;
import com.kuaishou.serveree.themis.component.service.CheckDuplicationService;
import com.kuaishou.serveree.themis.component.service.CheckExecutionService;
import com.kuaishou.serveree.themis.component.service.CheckIssueService;
import com.kuaishou.serveree.themis.component.service.CheckMeasuresService;
import com.kuaishou.serveree.themis.component.service.CheckMeasuresSnapshotService;
import com.kuaishou.serveree.themis.component.service.CheckMetricService;
import com.kuaishou.serveree.themis.component.service.CheckRepoBranchService;
import com.kuaishou.serveree.themis.component.service.CheckRepoService;
import com.kuaishou.serveree.themis.component.service.IssueChangesService;
import com.kuaishou.serveree.themis.component.service.IssueSummaryBaseService;
import com.kuaishou.serveree.themis.component.service.IssueSummaryService;
import com.kuaishou.serveree.themis.component.service.PCheckBaseService;
import com.kuaishou.serveree.themis.component.service.PCheckConfigService;
import com.kuaishou.serveree.themis.component.service.PCheckExecutionService;
import com.kuaishou.serveree.themis.component.service.PCheckIssueService;
import com.kuaishou.serveree.themis.component.service.PCheckMeasuresService;
import com.kuaishou.serveree.themis.component.service.SonarPipelineMeasureService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformCheckActionService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformCommonService;
import com.kuaishou.serveree.themis.component.service.platform.notice.CheckRepoNoticeActionService;
import com.kuaishou.serveree.themis.component.service.platform.scan.ScannerHelper;
import com.kuaishou.serveree.themis.component.service.sonar.mode.ScanModeService;
import com.kuaishou.serveree.themis.component.utils.CommonUtils;
import com.kuaishou.serveree.themis.component.utils.GitUtils;
import com.kuaishou.serveree.themis.component.utils.IssueUtils;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.utils.SonarLogic;
import com.kuaishou.serveree.themis.component.utils.ThemisTaskTokenUtil;
import com.kuaishou.serveree.themis.component.vo.request.AppendCheckMeasureVo;
import com.kuaishou.serveree.themis.component.vo.request.CheckActionRequest;
import com.kuaishou.serveree.themis.component.vo.request.CheckIssueVo;
import com.kuaishou.serveree.themis.component.vo.request.CheckIssueVo.Complexity;
import com.kuaishou.serveree.themis.component.vo.request.CheckMeasureVo;
import com.kuaishou.serveree.themis.component.vo.request.DataAppendRequest;
import com.kuaishou.serveree.themis.component.vo.request.EditIssueVo;
import com.kuaishou.serveree.themis.component.vo.request.IssueVo;
import com.kuaishou.serveree.themis.component.vo.request.MeasureVo;
import com.kuaishou.serveree.themis.component.vo.request.PipelineReportRequest;
import com.kuaishou.serveree.themis.component.vo.response.CheckActionResponse;
import com.kuaishou.serveree.themis.component.vo.response.DataAppendResponse;
import com.kuaishou.serveree.themis.component.vo.response.PipelineReportResponse;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/8/27 6:21 下午
 */
@Slf4j
@Service("PlatformCheckActionServiceImpl")
public class PlatformCheckActionServiceImpl implements PlatformCheckActionService {
    @Autowired
    private CheckIssueService checkIssueService;
    @Autowired
    private CheckRepoService checkRepoService;
    @Autowired
    private CheckRepoBranchService checkRepoBranchService;
    @Autowired
    private CheckDuplicationService checkDuplicationService;
    @Autowired
    private CheckBaseService checkBaseService;
    @Autowired
    private CheckExecutionService checkExecutionService;
    @Autowired
    private IssueSummaryService issueSummaryService;
    @Autowired
    private CheckComplexityService checkComplexityService;
    @Autowired
    private CheckMeasuresService checkMeasuresService;
    @Autowired
    private CheckMetricService checkMetricService;
    @Autowired
    private CheckMeasuresSnapshotService checkMeasuresSnapshotService;
    @Autowired
    private PlatformCommonService platformCommonService;
    @Autowired
    private IssueChangesService issueChangesService;
    @Autowired
    private KsRedisClient ksRedisClient;
    @Autowired
    private PCheckBaseService pCheckBaseService;
    @Autowired
    private PCheckConfigService pCheckConfigService;
    @Autowired
    private PCheckExecutionService pCheckExecutionService;
    @Autowired
    private PCheckIssueService pCheckIssueService;
    @Autowired
    private List<ScanModeService> scanModeServices;
    @Autowired
    private ScannerHelper scannerHelper;
    @Autowired
    private SonarPipelineMeasureService sonarPipelineMeasureService;
    @Autowired
    private IssueSummaryBaseService issueSummaryBaseService;
    @Autowired
    private PCheckMeasuresService pCheckMeasuresService;
    @Value("${kafka.code_check_result_notify_topic}")
    private String codeCheckResultNotifyTopic;
    @Autowired
    private CheckRepoNoticeActionService checkRepoNoticeActionService;
    @Autowired
    private SonarLogic sonarLogic;
    @Value("${skyeye-scanner.issue-list-url}")
    private String skyeyeIssueUrl;

    @Transactional
    @Override
    public CheckActionResponse action(CheckActionRequest request) {
        log.info("PlatformCheckActionServiceImpl action is {}", JSONUtils.serialize(request));
        // 校验一下合理性
        this.dataValidate(request);
        String source = ThemisTaskTokenUtil.get().getSource();
        // 先保存一下记录
        CheckRepo checkRepo = checkRepoService.initPlatform(request.getGitProjectId(), request.getBranch(), source);
        if (checkRepo == null) {
            throw new ThemisException(-1, "仓库未接入");
        }
        // 鉴权
        if (this.validatePermission(checkRepo, source)) {
            checkRepoService.updateById(checkRepo);
        }
        String branch = CommonUtils.defaultBranchIfEmpty(request.getBranch());
        CheckRepoBranch checkRepoBranch = checkRepoBranchService.init(checkRepo.getId(), branch);
        CheckBase checkBase = this.fillCheckBase(checkRepo, checkRepoBranch, request);
        checkBaseService.save(checkBase);
        CheckExecution checkExecution =
                this.fillCheckExecution(checkBase, ProcessExecutionReferType.getBySource(source).getType());
        checkExecutionService.save(checkExecution);
        // 保存用户的所有请求参数，来进行问题回溯 已经很稳定了 可以去掉了
        List<CheckIssue> checkIssues = Lists.newArrayList();
        // 需要保存的宽数据的映射map
        Map<Integer, List<CheckDuplication>> duplicationMap = Maps.newHashMap();
        Map<Integer, CheckComplexity> complexityMap = Maps.newHashMap();
        // 处理新增issue
        this.dealIssues(request.getIssues(), checkIssues, duplicationMap, complexityMap, checkRepoBranch,
                checkExecution, checkRepo);
        List<CheckIssue> needEditIssues = Lists.newArrayList();
        List<CheckComplexity> needEditComplexities = Lists.newArrayList();
        List<CheckDuplication> needSaveDuplications = Lists.newArrayList();
        List<Long> needDelDuplicationIds = Lists.newArrayList();
        // 处理修改issue
        this.dealEditIssues(request.getEditIssues(), needSaveDuplications, needEditComplexities,
                checkExecution, needEditIssues, needDelDuplicationIds);
        List<CheckMeasures> needSaveMeasures = Lists.newArrayList();
        List<Long> needDelMeasureIds = Lists.newArrayList();
        // 处理度量数据
        this.dealCheckMeasures(request.getMeasures(), checkExecution, checkRepoBranch, needDelMeasureIds,
                needSaveMeasures);
        // 变更snapshot
        Pair<List<Long>, CheckSnapshot> changePair =
                platformCommonService.changeSnapshot(checkBase, checkExecution.getId());
        CheckSnapshot checkSnapshot = changePair.getRight();
        // 操作整体数据
        // 度量数据相关
        if (CollectionUtils.isNotEmpty(needDelMeasureIds)) {
            checkMeasuresService.deleteBatchByIds(needDelMeasureIds);
        }
        if (CollectionUtils.isNotEmpty(needSaveMeasures)) {
            checkMeasuresService.saveBatch(needSaveMeasures);
        }
        Long checkRepoBranchId = checkRepoBranch.getId();
        List<CheckMeasures> checkMeasures = checkMeasuresService.listByCheckRepoBranchId(checkRepoBranchId);
        if (CollectionUtils.isNotEmpty(checkMeasures)) {
            List<CheckMeasuresSnapshot> measuresSnapshots = this.convert2Snapshots(checkMeasures, checkSnapshot);
            checkMeasuresSnapshotService.saveBatch(measuresSnapshots);
        }
        List<Integer> grayProjectIds = platformCommonService.grayGitProjectIdList();
        boolean grayProject = grayProjectIds.contains(checkRepo.getGitProjectId());
        // base issue相关
        if (CollectionUtils.isNotEmpty(checkIssues)) {
            Integer gitProjectId = checkRepo.getGitProjectId();
            // 自动生成issueUniqId
            if (grayProject) {
                generateIssueUniqId(checkIssues, checkExecution, gitProjectId);
            }
            checkIssueService.saveBatch(checkIssues);
        }
        if (CollectionUtils.isNotEmpty(needEditIssues)) {
            if (grayProject) {
                generateIssueUniqId(checkIssues, checkExecution, checkRepo.getGitProjectId());
            }
            checkIssueService.updateBatchById(needEditIssues);
        }
        if (grayProject) {
            // 处理issueSummary
            Pair<List<IssueSummary>, List<IssueSummary>> saveUpdatePair =
                    dealIssueSummary(checkIssues, needEditIssues, checkRepo.getGitProjectId(), checkBase);
            List<IssueSummary> needSaveList = saveUpdatePair.getKey();
            List<IssueSummary> needUpdateList = saveUpdatePair.getValue();
            if (CollectionUtils.isNotEmpty(needSaveList)) {
                for (IssueSummary issueSummary : needSaveList) {
                    issueSummary.setExecutionReferType(checkExecution.getReferType());
                    if (StringUtils.isEmpty(issueSummary.getCommonIssueUniqId())) {
                        issueSummary.setCommonIssueUniqId(IssueUtils.genIssueUniqId(issueSummary));
                    }
                }
                issueSummaryService.saveBatch(needSaveList);
            }
            if (CollectionUtils.isNotEmpty(needUpdateList)) {
                for (IssueSummary issueSummary : needUpdateList) {
                    issueSummary.setExecutionReferType(checkExecution.getReferType());
                    if (StringUtils.isEmpty(issueSummary.getCommonIssueUniqId())) {
                        issueSummary.setCommonIssueUniqId(IssueUtils.genIssueUniqId(issueSummary));
                    }
                }
                issueSummaryService.updateBatchByProjectBranchUniqId(needUpdateList);
            }
            issueChangesService.packageChangesIssues(
                    Lists.newArrayList(CollectionUtil.union(needSaveList, needUpdateList)));
            // 更新 base 表数据
            issueSummaryBaseService.batchCreateOrUpdate(CollectionUtils.union(needSaveList, needUpdateList));
        }
        // 重复度分组相关
        if (MapUtils.isNotEmpty(duplicationMap) || CollectionUtils.isNotEmpty(needSaveDuplications)) {
            List<CheckDuplication> duplications =
                    this.fillIssueId2Duplications(duplicationMap, checkIssues, needSaveDuplications);
            checkDuplicationService.saveBatch(duplications);
        }
        if (CollectionUtils.isNotEmpty(needDelDuplicationIds)) {
            checkDuplicationService.deleteBatchByIds(needDelDuplicationIds);
        }
        // 复杂度分组相关
        if (MapUtils.isNotEmpty(complexityMap)) {
            List<CheckComplexity> complexities = this.fillIssueId2Complexity(complexityMap, checkIssues);
            checkComplexityService.saveBatch(complexities);
        }
        if (CollectionUtils.isNotEmpty(needEditComplexities)) {
            checkComplexityService.updateBatchById(needEditComplexities);
        }
        List<CheckBase> dbCheckBase = checkBaseService.listByCheckRepoBranchId(checkRepoBranchId);
        Long baseId = checkBase.getId();
        List<CheckBase> needChangeBaseList = dbCheckBase.stream()
                .filter(o -> !baseId.equals(o.getId()))
                .map(o -> o.setIsLast(false))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(needChangeBaseList)) {
            checkBaseService.updateBatchById(needChangeBaseList);
        }
        checkBase.setIsLast(true);
        checkBase.setCommitId(request.getCommitId());
        checkBase.setGmtModified(LocalDateTime.now());
        checkBaseService.updateById(checkBase);
        deleteCache(checkRepo, checkRepoBranch);
        return new CheckActionResponse().setBaseId(checkBase.getId());
    }

    private void deleteCache(CheckRepo checkRepo, CheckRepoBranch checkRepoBranch) {
        String redisKey = KsRedisPrefixConstant.PLATFORM_CODE_QUALITY_SCORE_INFO + ":" + checkRepo.getGitProjectId()
                + ":" + checkRepoBranch.getBranchName();
        ksRedisClient.sync().del(redisKey);
    }

    @Override
    public DataAppendResponse dataAppend(DataAppendRequest reportRequest) {
        Long baseId = reportRequest.getBaseId();
        CheckBase checkBase = checkBaseService.getById(baseId);
        if (checkBase == null) {
            throw new ThemisException(NOT_FOUND_BASE_DATA);
        }
        List<AppendCheckMeasureVo> measures = reportRequest.getMeasures();
        if (CollectionUtils.isEmpty(measures)) {
            return DataAppendResponse.builder().appendCount(0).build();
        }
        CheckExecution checkExecution = checkExecutionService.getByBaseId(baseId);
        Long checkRepoId = checkBase.getCheckRepoId();
        Long checkRepoBranchId = checkBase.getCheckRepoBranchId();
        // 直接append数据
        List<CheckMeasures> checkMeasures = checkMeasuresService.append(measures, baseId, checkExecution.getId(),
                checkRepoId, checkRepoBranchId);
        checkMeasuresSnapshotService.saveSnapshotMeasures(checkMeasures, 0L);
        return DataAppendResponse.builder().appendCount(checkMeasures.size()).build();
    }

    @Override
    @Transactional
    public PipelineReportResponse pipelineReport(PipelineReportRequest reportRequest) {
        PCheckBase pCheckBase = pCheckBaseService.getByBuildId(reportRequest.getKspBuildId());
        if (pCheckBase == null) {
            pCheckBase = initProcessCheckBase(reportRequest);
            pCheckBaseService.save(pCheckBase);
        }
        PCheckConfig pCheckConfig = initPCheckConfig(pCheckBase);
        pCheckConfigService.initConfig(pCheckConfig);
        String source = ThemisTaskTokenUtil.get().getSource();
        PCheckExecution pCheckExecution = initPCheckExecution(pCheckBase, reportRequest, source);
        pCheckExecutionService.save(pCheckExecution);
        List<PCheckIssue> pCheckIssues = initPCheckIssues(pCheckBase, pCheckExecution, reportRequest);
        // 兼容可能会出现相同的两个issue的问题
        pCheckIssues = IssueUtils.filterDuplicatePCheckIssue(pCheckIssues);
        // 构造summary数据
        // 这里直接判断
        ScanModeService scanModeService = scanModeServices.stream()
                .filter(o -> o.incrementMode() == reportRequest.getIncrementMode())
                .findFirst()
                .orElseThrow(() -> new RuntimeException("未找到有效的扫描模式"));
        ScanModeContext scanModeContext = new ScanModeContext();
        scanModeContext.setPCheckBase(pCheckBase);
        scanModeContext.setPCheckExecution(pCheckExecution);
        scanModeContext.setPCheckIssueList(pCheckIssues);
        scanModeService.preAction(scanModeContext);
        // 都删除一下 防止用户再次执行
        // 这里根据用户的token来判断是哪种扫描器
        List<PCheckIssue> dbPCheckIssues = pCheckIssueService.listByPBaseIdAndType(pCheckBase.getId(),
                ProcessExecutionReferType.getBySource(source).getType());
        if (CollectionUtils.isNotEmpty(dbPCheckIssues)) {
            pCheckIssueService.deleteBatchByIds(dbPCheckIssues.stream().map(PCheckIssue::getId).collect(toList()));
        }
        if (CollectionUtils.isNotEmpty(pCheckIssues)) {
            pCheckIssueService.saveBatch(pCheckIssues);
        }
        SonarPipelineMeasure sonarMeasure = sonarLogic.getPipelineMeasure(pCheckIssues, pCheckBase);
        sonarMeasure.setKspPipelineId(reportRequest.getPipelineId());
        SonarPipelineMeasure dbMeasure = sonarPipelineMeasureService.getByKspBuildId(pCheckBase.getKspBuildId());
        if (dbMeasure != null) {
            sonarMeasure.setId(dbMeasure.getId());
            sonarMeasure.setGmtCreate(dbMeasure.getGmtCreate());
        }
        sonarPipelineMeasureService.saveOrUpdate(sonarMeasure);
        // 处理measure数据
        List<PCheckMeasures> pCheckMeasuresList = getPCheckMeasures(reportRequest.getMeasureList(), pCheckExecution);
        if (CollectionUtils.isNotEmpty(pCheckMeasuresList)) {
            pCheckMeasuresService.saveBatch(pCheckMeasuresList);
        }
        sendCrKafkaMsg(pCheckBase);
        if (reportRequest.isSendKimNotice()) {
            ScannerSendKimContext context = ScannerSendKimContext.builder()
                    .scanner(PlatformScannerEnum.getEnumBySource(source).getScanner())
                    .pCheckIssueList(pCheckIssues)
                    .pCheckExecution(pCheckExecution)
                    .pCheckBase(pCheckBase)
                    .build();
            MoreFunctions.runCatching(() -> scannerHelper.sendPipelineKimNotice(context));
        }
        // 新逻辑，发送流水线扫描通知
        checkRepoNoticeActionService.sendPipelineScanNotice((ScannerSendKimContext.builder()
                .scanner(PlatformScannerEnum.getEnumBySource(source).getScanner())
                .pCheckBase(pCheckBase)
                .pCheckIssueList(pCheckIssues)
                .pCheckExecution(pCheckExecution)
                .build()));
        return PipelineReportResponse.builder()
                .pBaseId(pCheckBase.getId())
                .build();
    }

    private List<PCheckMeasures> getPCheckMeasures(List<MeasureVo> measureList, PCheckExecution pCheckExecution) {
        if (CollectionUtils.isEmpty(measureList)) {
            return null;
        }
        LocalDateTime now = LocalDateTime.now();
        return measureList.stream().map(measureVo -> {
            PCheckMeasures pCheckMeasures = new PCheckMeasures();
            pCheckMeasures.setPBaseId(pCheckExecution.getPBaseId());
            pCheckMeasures.setPExecutionId(pCheckExecution.getId());
            pCheckMeasures.setGmtCreate(now);
            pCheckMeasures.setGmtModified(now);
            pCheckMeasures.setMetricKey(measureVo.getKey());
            pCheckMeasures.setMetricValue(measureVo.getVal());
            return pCheckMeasures;
        }).collect(toList());
    }

    private void sendCrKafkaMsg(PCheckBase pCheckBase) {
        CodeScanResultNotify codeScanResultNotify = CodeScanResultNotify.builder()
                .buildId(pCheckBase.getKspBuildId())
                .projectId(pCheckBase.getProjectId())
                .branch(pCheckBase.getBranch())
                .scanTypeName("前端天眼扫描")
                .build();
        KafkaProducers.sendString(codeCheckResultNotifyTopic,
                JSONUtils.serialize(codeScanResultNotify));
    }

    private List<PCheckIssue> initPCheckIssues(PCheckBase pCheckBase, PCheckExecution pCheckExecution,
            PipelineReportRequest reportRequest) {
        List<IssueVo> issueList = reportRequest.getIssueList();
        List<PCheckIssue> pCheckIssues = Lists.newArrayList();
        LocalDateTime now = LocalDateTime.now();
        for (IssueVo issueVo : issueList) {
            PCheckIssue pCheckIssue = new PCheckIssue();
            pCheckIssue.setStartLine(issueVo.getStartLine());
            pCheckIssue.setEndLine(issueVo.getEndLine());
            pCheckIssue.setStartOffset(issueVo.getStartOffset());
            pCheckIssue.setEndOffset(issueVo.getEndOffset());
            pCheckIssue.setRule(issueVo.getRule());
            pCheckIssue.setType(issueVo.getType());
            pCheckIssue.setStatus(issueVo.getStatus());
            pCheckIssue.setExecutionReferType(
                    ProcessExecutionReferType.getBySource(ThemisTaskTokenUtil.get().getSource()).getType()
            );
            pCheckIssue.setLocation(issueVo.getLocation());
            pCheckIssue.setAuthor(issueVo.getAuthor());
            pCheckIssue.setMessage(issueVo.getMessage());
            pCheckIssue.setGitLink(
                    GitUtils.getGitLink(pCheckBase.getRepoUrl(), pCheckBase.getBranch(), pCheckIssue.getLocation(),
                            pCheckIssue.getStartLine(), pCheckIssue.getEndLine())
            );
            pCheckIssue.setIssueLink(
                    String.format(skyeyeIssueUrl, pCheckBase.getKspBuildId()) + "&issueKeys=" + pCheckIssue.getIssueUniqId()
            );
            pCheckIssue.setPBaseId(pCheckBase.getId());
            pCheckIssue.setPExecutionId(pCheckExecution.getId());
            pCheckIssue.setSeverity(issueVo.getSeverity());
            pCheckIssue.setGmtCreate(now);
            pCheckIssue.setGmtModified(now);
            pCheckIssue.setIssueUniqIdV2(IssueUtils.genIssueUniqIdV2(pCheckIssue, reportRequest.getGitProjectId(),
                    pCheckExecution.getReferType()));
            pCheckIssue.setIssueUniqId(IssueUtils.genIssueUniqId(pCheckIssue, reportRequest.getGitProjectId()));
            pCheckIssue.setCommonIssueUniqId(IssueUtils.genIssueUniqId(pCheckIssue, pCheckBase.getProjectId()));
            // skyeye没有等级设置，只有数量设置
            pCheckIssue.setInDiff(true);
            pCheckIssue.setValidStuck(true);
            pCheckIssues.add(pCheckIssue);
        }
        return pCheckIssues;
    }

    private PCheckExecution initPCheckExecution(PCheckBase pCheckBase, PipelineReportRequest reportRequest,
            String source) {
        LocalDateTime now = LocalDateTime.now();
        return PCheckExecution.builder()
                .pBaseId(pCheckBase.getId())
                .referType(ProcessExecutionReferType.getBySource(source).getType())
                .sync(true)
                .incrementMode(reportRequest.getIncrementMode())
                .incrementType(IncrementType.FILE.getType())
                .gmtModified(now)
                .gmtCreate(now)
                .build();
    }

    private PCheckConfig initPCheckConfig(PCheckBase pCheckBase) {
        LocalDateTime now = LocalDateTime.now();
        return PCheckConfig.builder()
                .pBaseId(pCheckBase.getId())
                .sponsorType(ProcessSponsorType.PIPELINE.getType())
                .gmtCreate(now)
                .gmtModified(now)
                .build();
    }

    private PCheckBase initProcessCheckBase(PipelineReportRequest reportRequest) {
        LocalDateTime now = LocalDateTime.now();
        return PCheckBase.builder()
                .branch(reportRequest.getBranch())
                .commitId(reportRequest.getCommitId())
                .repoUrl(reportRequest.getRepoUrl())
                .gmtCreate(now)
                .gmtModified(now)
                .kspBuildId(reportRequest.getKspBuildId())
                .projectId(reportRequest.getGitProjectId())
                .sponsor(reportRequest.getSponsor())
                .stuckStatus(reportRequest.getStuckStatus())
                .mrId(reportRequest.getMrId() == null ? 0 : reportRequest.getMrId())
                .scannerType(ProcessScannerType.getByTokenSource(ThemisTaskTokenUtil.get().getSource()).getType())
                .build();
    }

    private Pair<List<IssueSummary>, List<IssueSummary>> dealIssueSummary(List<CheckIssue> checkIssues,
            List<CheckIssue> needEditIssues, Integer gitProjectId, CheckBase checkBase) {
        Map<String, IssueSummaryBase> uniqIdBaseSummaryMap =
                issueSummaryBaseService.aggragateIssueUniqIdSummaryBaseMap(gitProjectId);
        List<IssueSummary> saveIssueSummaries = Lists.newArrayList();
        List<IssueSummary> editIssueSummaries = Lists.newArrayList();
        // 这里兼容之前的数据逻辑
        if (MapUtils.isEmpty(uniqIdBaseSummaryMap)) {
            Collection<CheckIssue> union = CollectionUtil.union(checkIssues, needEditIssues);
            fillUpIssueSummaries(saveIssueSummaries, union);
            return Pair.of(saveIssueSummaries, editIssueSummaries);
        }
        // 查询所有issue在同分支上的issueSummary记录
        List<String> allIssueUniqIds =
                CollectionUtils.union(needEditIssues, checkIssues).stream().map(CheckIssue::getIssueUniqId)
                        .distinct().collect(Collectors.toList());
        Map<String, IssueSummary> sameBranchSummaryMap =
                issueSummaryService.sameBranchSummary(allIssueUniqIds, checkBase.getBranch(),
                        ScanModeEnum.OFFLINE.getCode());
        LocalDateTime now = LocalDateTime.now();
        for (CheckIssue checkIssue : checkIssues) {
            IssueSummaryBase summaryBase = uniqIdBaseSummaryMap.get(checkIssue.getIssueUniqId());
            String effectStatus = issueSummaryBaseService.getEffectStatus(summaryBase);
            IssueSummary issueSummary = sameBranchSummaryMap.get(checkIssue.getIssueUniqId());
            if (issueSummary == null) {
                issueSummary = new IssueSummary();
                BeanUtil.copyProperties(checkIssue, issueSummary, "id");
                issueSummary.setStatus(StringUtils.defaultString(effectStatus, CheckIssueStatus.OPEN.getStatus()));
                saveIssueSummaries.add(issueSummary);
            } else {
                issueSummary.setStatus(StringUtils.defaultString(effectStatus, CheckIssueStatus.OPEN.getStatus()));
                issueSummary.setGmtModified(now);
                editIssueSummaries.add(issueSummary);
            }
        }
        for (CheckIssue needEditIssue : needEditIssues) {
            IssueSummaryBase summaryBase = uniqIdBaseSummaryMap.get(needEditIssue.getIssueUniqId());
            String effectStatus = issueSummaryBaseService.getEffectStatus(summaryBase);
            IssueSummary issueSummary = sameBranchSummaryMap.get(needEditIssue.getIssueUniqId());
            if (issueSummary == null) {
                issueSummary = new IssueSummary();
                BeanUtil.copyProperties(needEditIssue, issueSummary, "id");
                issueSummary.setStatus(StringUtils.defaultString(effectStatus, needEditIssue.getStatus()));
                saveIssueSummaries.add(issueSummary);
            } else {
                issueSummary.setStatus(StringUtils.defaultString(effectStatus, needEditIssue.getStatus()));
                issueSummary.setGmtModified(now);
                editIssueSummaries.add(issueSummary);
            }
        }
        fillUpIssueSummaries(saveIssueSummaries, editIssueSummaries);
        return Pair.of(saveIssueSummaries, editIssueSummaries);
    }

    private void fillUpIssueSummaries(List<IssueSummary> saveIssueSummaries, List<IssueSummary> editIssueSummaries) {
        if (CollectionUtils.isNotEmpty(saveIssueSummaries)) {
            for (IssueSummary saveIssueSummary : saveIssueSummaries) {
                saveIssueSummary.setScanMode(ScanModeEnum.OFFLINE.getCode());
            }
        }
        if (CollectionUtils.isNotEmpty(editIssueSummaries)) {
            for (IssueSummary editIssueSummary : editIssueSummaries) {
                editIssueSummary.setScanMode(ScanModeEnum.OFFLINE.getCode());
            }
        }
    }

    private void fillUpIssueSummaries(List<IssueSummary> saveIssueSummaries, Collection<CheckIssue> checkIssues) {
        for (CheckIssue checkIssue : checkIssues) {
            IssueSummary issueSummary = new IssueSummary();
            BeanUtil.copyProperties(checkIssue, issueSummary, "id");
            issueSummary.setScanMode(ScanModeEnum.OFFLINE.getCode());
            saveIssueSummaries.add(issueSummary);
        }
    }

    private void generateIssueUniqId(List<CheckIssue> checkIssues, CheckExecution checkExecution, Integer gitProjectId) {
        for (CheckIssue checkIssue : checkIssues) {
            String issueUniqId = checkIssue.getIssueUniqId();
            if (StringUtils.isNotEmpty(issueUniqId)) {
                continue;
            }
            checkIssue.setIssueUniqIdV2(IssueUtils.genIssueUniqIdV2(checkIssue, gitProjectId, checkExecution.getReferType()));
            checkIssue.setIssueUniqId(IssueUtils.genIssueUniqId(checkIssue, gitProjectId));
            checkIssue.setCommonIssueUniqId(checkIssue.getIssueUniqId());
        }
    }

    public void dealCheckMeasures(List<CheckMeasureVo> measures, CheckExecution checkExecution,
            CheckRepoBranch checkRepoBranch, List<Long> needDelMeasureIds, List<CheckMeasures> needSaveMeasures) {
        if (CollectionUtils.isEmpty(measures)) {
            return;
        }
        List<CheckMeasures> dbCheckMeasures = checkMeasuresService.listByCheckRepoBranchId(checkRepoBranch.getId());
        if (CollectionUtils.isEmpty(dbCheckMeasures)) {
            measures.forEach(requestMeasure -> this.add2SaveList(needSaveMeasures, checkExecution, checkRepoBranch,
                    requestMeasure.getKey(), requestMeasure.getValue()));
            return;
        }
        Map<String, List<CheckMeasures>> dbKeyMeasuresMap =
                dbCheckMeasures.stream().collect(Collectors.groupingBy(CheckMeasures::getMetricKey));
        measures.forEach(requestMeasure -> {
            String key = requestMeasure.getKey();
            List<CheckMeasures> checkMeasures = dbKeyMeasuresMap.get(key);
            if (CollectionUtils.isNotEmpty(checkMeasures)) {
                needDelMeasureIds.addAll(checkMeasures.stream().map(CheckMeasures::getId).collect(toList()));
            }
            this.add2SaveList(needSaveMeasures, checkExecution, checkRepoBranch, requestMeasure.getKey(),
                    requestMeasure.getValue());
        });
    }

    public void dealEditIssues(List<EditIssueVo> editIssueVos, List<CheckDuplication> needSaveDuplications,
            List<CheckComplexity> needEditComplexities, CheckExecution checkExecution,
            List<CheckIssue> needEditIssues, List<Long> needDelDuplicationIds) {
        if (CollectionUtils.isEmpty(editIssueVos)) {
            return;
        }
        Map<Long, List<EditIssueVo>> requestIssueIdEditMap = Maps.newConcurrentMap();
        List<Long> complexityIssueIds = Lists.newArrayList();
        List<Long> duplicationIssueIds = Lists.newArrayList();
        List<CheckIssue> needUpdateCheckIssues = Lists.newArrayList();
        // 数据库中查询出原有的issue数据
        Set<Long> needEditIssueSet = editIssueVos.stream().map(EditIssueVo::getIssueId).collect(Collectors.toSet());
        Map<Long, CheckIssue> issueIdCheckIssueMap = checkIssueService.listIssuesInIds(needEditIssueSet)
                .stream().collect(Collectors.toMap(CheckIssue::getId, Function.identity()));
        editIssueVos.forEach(editIssueVo -> {
            // map变更
            Long issueId = editIssueVo.getIssueId();
            CommonUtils.getOrCreate(issueId, requestIssueIdEditMap, CopyOnWriteArrayList::new).add(editIssueVo);
            CheckIssue dbCheckIssue = issueIdCheckIssueMap.get(issueId);
            if (dbCheckIssue != null) {
                // 分组
                if (CheckIssueType.DUPLICATION.getType().equals(dbCheckIssue.getType())) {
                    duplicationIssueIds.add(issueId);
                } else if (CheckIssueType.COMPLEXITY.getType().equals(dbCheckIssue.getType())) {
                    complexityIssueIds.add(issueId);
                } else {
                    needUpdateCheckIssues.add(dbCheckIssue);
                }
            }
        });
        // 分类做修改
        if (CollectionUtils.isNotEmpty(complexityIssueIds)) {
            this.dealEditComplexityIssues(complexityIssueIds, requestIssueIdEditMap, issueIdCheckIssueMap,
                    needEditIssues, checkExecution, needEditComplexities);
        }
        if (CollectionUtils.isNotEmpty(duplicationIssueIds)) {
            this.dealEditDuplicationsIssues(duplicationIssueIds, requestIssueIdEditMap, issueIdCheckIssueMap,
                    checkExecution, needSaveDuplications, needDelDuplicationIds, needEditIssues);
        }
        if (CollectionUtils.isNotEmpty(needUpdateCheckIssues)) {
            this.dealEditCommonIssues(needUpdateCheckIssues, needEditIssues, requestIssueIdEditMap, checkExecution);
        }
    }

    public void dealEditDuplicationsIssues(List<Long> duplicationIssueIds, Map<Long, List<EditIssueVo>> issueIdEditMap,
            Map<Long, CheckIssue> issueIdCheckIssueMap, CheckExecution checkExecution,
            List<CheckDuplication> needSaveDuplications, List<Long> needDelDuplicationIds,
            List<CheckIssue> needEditIssues) {
        List<CheckDuplication> dbDuplicationsList = checkDuplicationService.listByIssueIds(duplicationIssueIds);
        Map<Long, List<CheckDuplication>> issueIdDuplicationsMap = dbDuplicationsList.stream()
                .collect(Collectors.groupingBy(CheckDuplication::getCheckIssueId));
        issueIdDuplicationsMap.forEach((issueId, dbDuplications) -> {
            List<EditIssueVo> editIssueVoList = issueIdEditMap.get(issueId);
            EditIssueVo editIssueVo = editIssueVoList.get(editIssueVoList.size() - 1);
            CheckIssue checkIssue = issueIdCheckIssueMap.get(issueId);
            this.refreshCheckIssue(checkIssue, editIssueVo, checkExecution);
            needEditIssues.add(checkIssue);
            // 处理关联的重复度数据
            editIssueVo.getDuplications().forEach(duplicationVo -> {
                CheckDuplication checkDuplication = new CheckDuplication();
                BeanUtil.copyProperties(checkIssue, checkDuplication, "id");
                checkDuplication.setCheckIssueId(issueId);
                checkDuplication.setKee(issueId.toString());
                checkDuplication.setStartLine(duplicationVo.getStartLine());
                checkDuplication.setEndLine(duplicationVo.getEndLine());
                checkDuplication.setLocation(duplicationVo.getLocation());
                checkDuplication.setGitLink(duplicationVo.getGitLink());
                needSaveDuplications.add(checkDuplication);
            });
            needDelDuplicationIds.addAll(dbDuplications.stream().map(CheckDuplication::getId).collect(toList()));
        });
    }

    private void dealEditComplexityIssues(List<Long> complexityIssueIds, Map<Long, List<EditIssueVo>> issueIdEditMap,
            Map<Long, CheckIssue> issueIdCheckIssueMap, List<CheckIssue> needEditIssues, CheckExecution checkExecution,
            List<CheckComplexity> needEditComplexities) {
        List<CheckComplexity> dbComplexities = checkComplexityService.listByIssueIds(complexityIssueIds);
        dbComplexities.forEach(checkComplexity -> {
            Long checkIssueId = checkComplexity.getCheckIssueId();
            List<EditIssueVo> editIssueVoList = issueIdEditMap.get(checkIssueId);
            EditIssueVo editIssueVo = editIssueVoList.get(editIssueVoList.size() - 1);
            CheckIssue checkIssue = issueIdCheckIssueMap.get(checkIssueId);
            this.refreshCheckIssue(checkIssue, editIssueVo, checkExecution);
            needEditIssues.add(checkIssue);
            // 处理关联的复杂度数据
            BeanUtil.copyProperties(checkIssue, checkComplexity, "id", "type", "value");
            Complexity complexity = editIssueVo.getComplexity();
            checkComplexity.setValue(complexity.getCycleComplexity());
            checkComplexity.setGmtModified(LocalDateTime.now());
            needEditComplexities.add(checkComplexity);
        });
    }

    public void dataValidate(CheckActionRequest reportRequest) {
        Preconditions.checkArgument(StringUtils.isNotEmpty(reportRequest.getCommitId()), "commitId must not be empty");
        List<CheckIssueVo> issues = reportRequest.getIssues();
        if (CollectionUtils.isNotEmpty(issues)) {
            issues.forEach(issueVo -> {
                if (!CheckIssueType.correctType(issueVo.getType())) {
                    throw new IllegalArgumentException("find illegal check issue type");
                }
                if (StringUtils.isEmpty(issueVo.getLocation())) {
                    throw new IllegalArgumentException("find illegal location");
                }
                if (!CheckIssueSeverity.correctType(issueVo.getSeverity())) {
                    throw new IllegalArgumentException("find illegal severity");
                }
                if (StringUtils.isEmpty(issueVo.getMessage())) {
                    throw new IllegalArgumentException("find illegal message");
                }
                if (StringUtils.isEmpty(issueVo.getRule())) {
                    throw new IllegalArgumentException("find illegal rule");
                }
                CheckIssueType checkIssueType = CheckIssueType.getEnumByType(issueVo.getType());
                switch (checkIssueType) {
                    case DUPLICATION:
                        if (CollectionUtils.isEmpty(issueVo.getDuplications())) {
                            throw new IllegalArgumentException("DUPLICATION type, duplications must not be null");
                        }
                        issueVo.getDuplications().forEach(duplicationVo -> {
                            if (StringUtils.isEmpty(duplicationVo.getLocation())) {
                                throw new IllegalArgumentException("duplication location must not be null");
                            }
                            if (duplicationVo.getStartLine() < 1) {
                                throw new IllegalArgumentException("duplication start line must great than 0");
                            }
                            if (duplicationVo.getEndLine() < 1) {
                                throw new IllegalArgumentException("duplication end line must great than 0");
                            }
                            if (duplicationVo.getEndLine() < duplicationVo.getStartLine()) {
                                throw new IllegalArgumentException("duplication endLine must not less than  startLine");
                            }
                        });
                        break;
                    case COMPLEXITY:
                        if (issueVo.getComplexity() == null) {
                            throw new IllegalArgumentException("COMPLEXITY type, complexity must not be null");
                        }
                        break;
                    default:
                        break;
                }
            });
        }
        List<EditIssueVo> editIssues = reportRequest.getEditIssues();
        if (CollectionUtils.isNotEmpty(editIssues)) {
            editIssues.forEach(editIssueVo -> {
                if (editIssueVo.getIssueId() == null) {
                    throw new IllegalArgumentException("find illegal issueId , edit issue issueId must not be null");
                }
            });
        }
        List<CheckMeasureVo> measures = reportRequest.getMeasures();
        if (CollectionUtils.isNotEmpty(measures)) {
            List<CheckMetric> allMetrics = checkMetricService.all();
            List<String> allKeys = allMetrics.stream().map(CheckMetric::getMetricKey).collect(toList());
            measures.forEach(measure -> {
                String key = measure.getKey();
                if (!allKeys.contains(key)) {
                    throw new IllegalArgumentException("find unfiled key:" + key);
                }
            });
        }
    }
}